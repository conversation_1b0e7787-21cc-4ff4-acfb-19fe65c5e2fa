<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/staff_menu/'); ?>">Staff Menu</a></li>
    <li><a href="<?php echo site_url('staff/staff_controller/'); ?>">Staff Index</a></li>
    <li>Staff Info</li>
</ul>

<hr>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between align-items-center" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff mb-0" style="margin-bottom:0;">
                        <a class="back_anchor" href="<?php echo site_url('staff/staff_controller/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Staff Information - <?= ($name_to_caps ? strtoupper($staff_name) : ($staff_name)) ?>
                    </h3>
                    <span class="info" style="font-size: 15px;font-weight:400">
                         Status - <?php echo $staff_status; ?>
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <?php
            $data['tile_list'] = $tiles;
            $data['heading'] = '';
            echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true);
            ?>

        </div>
        <div class="card-body pt-1">
            <?php
            $data['tile_list'] = $manage_attributes_tiles;
            $data['heading'] = 'Staff Attributes';
            echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true);
            ?>

        </div>
    </div>
</div>





<!-- <div class="col-md-12">
<div class="panel panel-default new-panel-style_3">
    <div class="panel-heading new-panel-heading">
        <h3 class="panel-title"><strong>Staff Information - <?= $staff_name ?></strong></h3>
    </div> -->
<!-- </div>


    <div class="panel panel-default new-panel-style_3"> -->
<!-- <div class="panel-body">

            <?php if ($permitStaffCrud) { ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a href="<?php echo site_url('staff/Staff_controller/addStaffAddress/' . $staff_uid) ?>">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/address.png">
                            </div>    
                            <div class="widget-data">
                                <div class="widget-title">Address</div>
                            </div>                           
                        </a>
                    </div> 
                </div>
            <?php } ?> -->

<!-- <div class="col-md-3">
                <div class="widget widget-default widget-item-icon">
                    <a href="<?php //echo site_url('management/payroll/addSalaryDetails/'.$staff_uid.'/1/1') 
                                ?>">
                    <div class="widget-item-left">
                        <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff+and+Admin+icons+/canteencoll.png">
                    </div>                             
                    <div class="widget-data">
                        <div class="widget-title">Salary</div>
                    </div>
                    </a>
                </div>
            </div> -->

<!-- <?php if ($permitStaffUserCreation) { ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a onclick="return confirm('Do you really want to reset password?');" href="<?php echo site_url('staff/Staff_controller/resetPassword/' . $staff_uid) ?>">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/initsubs.png">
                        </div>                             
                        <div class="widget-data">
                            <div class="widget-title">Reset Password</div>
                        </div>
                        </a>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a  href="<?php echo site_url('staff/Staff_controller/changeusername/' . $staff_uid) ?>">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/user.png">
                        </div>                             
                        <div class="widget-data">
                            <div class="widget-title">Provision User Login</div>
                        </div>
                        </a>
                    </div>
                </div>
            <?php } ?>
            
            <?php if ($this->authorization->isAuthorized('STAFF_OBSERVATION.VIEW_SUMMARY')) : ?>
            <div class="col-md-4">
                <div class="widget widget-default widget-item-icon new_height">
                    <a  href="<?php echo site_url('staff/observation/viewObservations/' . $staff_uid) ?>">
                    <div class="widget-item-left">
                        <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/staffobserv.png">
                    </div>                             
                    <div class="widget-data">
                        <div class="widget-title">Staff Observations</div>
                    </div>
                    </a>
                </div>
            </div>
            <?php endif ?>

            <?php if ($permitStaffCrud) { ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a  href="<?php echo site_url('staff/Staff_controller/editStaff/' . $staff_uid) ?>">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/stocklist.png">
                        </div>                             
                        <div class="widget-data">
                            <div class="widget-title">Update Data</div>
                        </div>
                        </a>
                    </div>
                </div>
            <?php } ?>

            <?php if ($permitProfileView) : ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a href="<?php echo site_url('staff/Staff_profile_view_controller/staff_profile/' . $staff_uid); ?>">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/staffinitiative.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Profile</div>
                            </div>                         
                        </a>
                    </div>                            
                </div>
            <?php endif ?>

            <?php if ($permit_staff_sms_report) : ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a href="<?php echo site_url('sms/staffSMSReport/' . $staff_uid); ?>">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/sms.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">SMS Report</div>
                            </div>                         
                        </a>
                    </div>                            
                </div>
            <?php endif ?>
            <?php if ($this->authorization->isSuperAdmin()) : ?>
                <div class="col-md-4">
                    <div class="widget widget-default widget-item-icon new_height">
                        <a onclick="return confirm('Do you really want to reset session?');" href="<?php echo site_url('staff/Staff_controller/resetSession/' . $staff_uid) ?>">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/initsubs.png">
                        </div>                             
                        <div class="widget-data">
                            <div class="widget-title">Reset Session</div>
                        </div>
                        </a>
                    </div>
                </div>
            <?php endif ?>  
        </div>
    </div>
</div>-->