<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('sales/sales') ?>">Sales Dashboard</a></li>
  <li class="active">Daily transactions</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
     <div class="panel-heading panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0">
        <h3 class="panel-title card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('sales/sales'); ?>">
            <span class="fa fa-arrow-left"></span>
          </a>
           Sales Daily Transaction Report
        </h3>
      </div>
    </div>

    <div class="panel-body">
      <div class="row" style="margin: 0px">
        <div class="col-md-12">
          <div class="col-md-2 form-group">
            <p>Date Range</p>
            <div id="reportrange" class="dtrange" style="width: 100%">                                            
              <span></span>
                <input type="hidden" id="from_date">
                <input type="hidden" id="to_date">
            </div>
          </div>

          <div class="col-md-2 form-group">
            <p>Category</p>
             <select class="form-control select" name="prodcut_varints"  multiple title='All' id="product_variants">
                <?php foreach($category as $cat){ ?>
                  <option value="<?php echo $cat->vId ?>"><?php echo $cat->varinat_name ?></option>
                <?php } ?>
              </select>
          </div>

          <div class="col-md-2 form-group">
            <p>Payment Mode</p>
            <select class="form-control select" name="payment_modes"  multiple title='All' id="payment_modes">
              <option value="9">Cash</option>
              <option value="4">Cheque</option>
              <option value="1">DD</option>
              <option value="7">Card</option>
              <option value="8">Net Banking</option>
              <option value="10">Online Link</option>
              <option value="11">UPI</option>
            </select>
          </div>

          <div class="col-sm-2 col-md-2" style="height: 4.5rem;">
            <p style="margin-top: 2rem"></p>
            <input type="button" name="search"  id="search" class="btn btn-primary" value="Get Report">
          </div>

        </div>

      </div>
    </div>

    <div class="result">
      <div class="col-md-12">
        <ul class="panel-controls mt-3" id="exportButtons" style="display: none;">
          <button id="stu_print" class="btn btn-danger" onclick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
          <a style="margin-left:3px;" onclick="exportToExcel_daily()" class="btn btn-primary pull-right">Export</a>
        </ul>
      </div>
      <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
          <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
          <h4>Sales Daily Fee Report</h4>
          <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
        </div>

        <div class="panel-body salesTrans table-responsive" id="daily_sales">
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<!-- 
<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="panel-heading new-panel-heading">
      <h3 class="panel-title"><strong>Sales Daily Transaction Report</strong></h3>
    </div>
    <div class="panel-body">
      <div class="col-md-9">
        <div class='col-sm-4'>
          <div class='form-group'>                       
            <label for="from_date" class="col-sm-5 control-label">From Date:</label>
            <div class="col-sm-7">              
              <input  type="text"  class="form-control" id="from_date" name="from_date" value="<?php echo date('d-m-Y'); ?>" >
            </div>
          </div>
        </div>
        <div class='col-sm-4'>
          <div class='form-group' >                       
            <label for="to_date" class="col-sm-5 control-label">To Date:</label>
            <div class="col-sm-7" >
              <input type="text" class="form-control" id="to_date"  name="to_date" value="<?php echo date('d-m-Y'); ?>" >
            </div>
          </div> 
        </div>
        <div class='col-sm-4'>
          <div class='form-group' >                       
            <label for="to_date" class="col-sm-5 control-label">Category:</label>
            <div class="col-sm-7" >
              <select class="form-control" name="prodcut_varints" id="product_variants">
                <option value="">Select Category</option>
                <?php foreach($category as $cat){ ?>
                  <option value="<?php echo $cat->vId ?>"><?php echo $cat->varinat_name ?></option>
                <?php } ?>
              </select>
             
            </div>
          </div> 
        </div>
      </div>
      <div class="col-md-2 form-group">
        <button type="button" id="search" class="btn btn-primary">Search</button>
      </div>
    </div>
  </div>

  <div class="panel panel-default new-panel-style_3">
    <ul class="panel-controls mt-3" id="exportButtons" style="display: none;">
      <button id="stu_print" class="btn btn-danger" onclick="printProfile()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
    </ul>
    <div id="printArea">
      <div id="print_visible" style="display: none;" class="text-center">
        <h3><?php //echo $this->settings->getSetting('school_name') ?></h3>
        <h4>Sales Daily Fee Report</h4>
        <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
      </div>

      <div class="panel-body salesTrans" id="daily_sales">
      </div>
    </div>
  </div>

</div>
 -->
<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     // 'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


  // $(document).ready(function(){
  //   $('#from_date, #to_date').datepicker({
  //     format: 'd-m-yyyy',
  //     "autoclose": true
  //   });

  // });
</script>


<script type="text/javascript">
  
  $('#search').click(function(){
    var fromDate = $('#from_date').val();
    var toDate = $('#to_date').val();
    var product_variants = $('#product_variants').val();
    var payment_modes = $('#payment_modes').val();
    
    $('#fromDate').html(fromDate);
    $('#toDate').html(toDate);
    $('#exportButtons').hide();
    $.ajax({
      url:'<?php echo site_url('sales/sales/get_daily_transaction') ?>',
      type:'post',
      data:{'fromDate':fromDate,'toDate':toDate,'product_variants':product_variants,'payment_modes':payment_modes},
      success:function(data){
        var daily = $.parseJSON(data);
        // console.log(daily);
        // $('#exportButtons').show();
        $('#daily_sales').html(constract_table_daily_transaction(daily));
        
        var totalSalesCollectedAmount = $('#totalSalesCollectedAmount').val();
         var table = $('#daily_sales_data_table').DataTable({
            paging: false, 
            autoWidth: false,
            dom: 'Bfrtip',
            buttons: [
              {
                extend: 'print',
                footer: true,
                text :'<button class="btn btn-danger"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>',
                messageTop: function () {
                    var html = '';
                    html += '<center>Daily Fee Report <br>From '+fromDate+' To '+toDate+'</center>';
                    html += '<br><label>Total Collected Amount : '+totalSalesCollectedAmount+'</label>';
                    return html;
                  },
                  exportOptions: {
                      columns: ':visible'
                  },
                  customize: function ( win ) {
                  
                    $(win.document.body)
                        .css( 'font-size', '10pt' )
                        
                    $(win.document.body).find( 'table' )
                        .addClass( 'compact' )
                        .css( 'font-size', 'inherit' );

                    var footer = $('tfoot');
                    if (footer.length > 0) {
                      var foot =  $(win.document.body).find('tfoot tr th:eq(0)').text('');
                      var foot1 =  $(win.document.body).find('tfoot tr th:eq(1)').text('');
                      var foot1 =  $(win.document.body).find('tfoot tr th:eq(2)').text('');
                      var foot1 =  $(win.document.body).find('tfoot tr th:eq(3)').text('');
                      var foot1 =  $(win.document.body).find('tfoot tr th:eq(4)').text('').css('border-top', 'none');
                      $(win.document.body).find('tfoot tr th:last-child').remove();
                      foot.before('<th style="border:none"></th>');
                    }   

                }
              },
              {
                extend: 'excelHtml5',
                text :'<button class="btn btn-primary"><span class="fa fa-file-text-o"></span> Export</button>',
                messageTop: function () {
                    var html = '';
                    html += 'Daily Fee Report From '+fromDate+' To '+toDate+'';
                     html += 'Total Collected Amount : '+totalSalesCollectedAmount+'';
                    return html;
                  },
                  exportOptions: {
                      columns: ':visible'
                  }
              }
            ]
            // order: [
            //   [1, 'asc']
            // ],
            // fnRowCallback : function(nRow, aData, iDisplayIndex){
            //     $("td:first", nRow).html(iDisplayIndex +1);
            //    return nRow;
            // },
          });

      }
    });
  });

  function constract_table_daily_transaction(daily_data) {
    // console.log(daily_data);
    var softDelete = '<?php echo $this->authorization->isAuthorized('SALES.SOFT_DELETE_RECEIPTS') ?>'; 
    var html ='';

    html +='<table class="table table-bordered table-responsive datatable" id="daily_sales_data_table" style="width:100%">';
    html +='<thead>';
    html +='<tr>';
    var totalAmount = 0;
    for(var smId in daily_data){
      totalAmount += parseFloat(daily_data[smId].total_amount);
    }

    var total_quantity= 0;
    for(var smId in daily_data){
      var arr= daily_data[smId].qty_receipt_wise.split('___');
      arr.pop()
      let sum = arr.reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue), 0);
      total_quantity += Number(sum);
    }
    html += '<input type="hidden" id="totalSalesCollectedAmount" value='+totalAmount+'>';
    html +='<td colspan="10" ><b>Total Amount :</b>'+totalAmount+' | <b>Total Quantity :</b>'+total_quantity+'</td>';
    html +='</tr>';
    html +='<tr>';
    html +='<th>#</th>';
    html +='<th>Receipt date.</th>';
    html +='<th>Receipt No.</th>';
    html +='<th class="printHide" style="display:none;" >Receipt No.</th>';
    html +='<th>Student Name</th>';
    html +='<th>Father Name</th>';
    html +='<th>Class/Section</th>';
    html +='<th style="min-width: 390px;">Products</th>';
    html +='<th>Total Quantity</th>';
    html +='<th>Total Amount</th>';
    html +='<th>Payment Type</th>';

    html +='<th>Bank Name</th>';
    html +='<th>Bank Branch Name</th>';
    html +='<th>DD Check Date</th>';
    html +='<th>Reference Number</th>';
    html +='<th>Remarks</th>';

    html +='<th class="printHide">Actions</th>';
    html +='</tr>';
    html +='</thead>';

    html +='<tbody>';
    var i=1;
    var reciept = "<?php echo site_url('sales/sales/sales_receipt_history') ?>";
    var download = "<?php echo site_url('sales/sales/sales_receipt_pdf_download') ?>";

    for(var smId in daily_data){
      var reciept_url = reciept + '/' + daily_data[smId].smId + '/' + daily_data[smId].student_id;
      var download_url = download + '/' + daily_data[smId].smId;
      var NC = '';          
      if (daily_data[smId].recon_status == 1) {
        NC = '<span style="color:red;"> <b> (N/C) </b><span>';
      }else if(daily_data[smId].recon_status == 2){
        NC = '<span> <b> (C) </b><span>';
      }
      var style = '';
      if(daily_data[smId].sales_type == 'new') {
        style = 'style="background:#e5e2ea;"';
      }
      
      html +='<tr '+style+'>';
      html +='<td>'+i+'</td>';
      html +='<td>'+daily_data[smId].receipt_date+'</td>';
      

      html +='<td class="printHideA"><a class="printHideA" target="_blank" href="'+reciept_url+'">'+daily_data[smId].receipt_no+'</a></td>';

      html +='<td class="printShow" style="display:none">'+daily_data[smId].receipt_no+'</td>';

      html += '<td>' + daily_data[smId].std_name + '<br>' + 
        (daily_data[smId].student_mobile_no ? daily_data[smId].student_mobile_no : '') + '</td>';
      html += '<td>' + (daily_data[smId].parent_name ? daily_data[smId].parent_name : '-') + '</td>';
      html +='<td>'+daily_data[smId].class_section+'</td>';
      html +='<td>'+daily_data[smId].products+'</td>';
      
      var arr= daily_data[smId].qty_receipt_wise.split('___');
      arr.pop()
      let sum = arr.reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue), 0);
      // total_quantity += Number(sum);


      html +='<td>'+sum+'</td>';
      html +='<td>'+daily_data[smId].total_amount+'</td>';
      html +='<td>'+daily_data[smId].payment_type+NC+'</td>';


      var cheque_dd_date= daily_data[smId].cheque_dd_date;
      if(daily_data[smId].payment_type == 'Cash') {
        cheque_dd_date = '';
      }



      html +='<td>'+daily_data[smId].bank_name || ""+'</td>';
      html +='<td>'+daily_data[smId].bank_branch || ""+'</td>';
      html +='<td>'+cheque_dd_date || ""+'</td>';
      html +='<td>'+daily_data[smId].cheque_dd_number || ""+'</td>';
      html +='<td>'+daily_data[smId].remarks || ""+'</td>';

      html +='<td class="printHide"  style="width: 7.5vw;"><a class="btn btn-info btn-xs printHideA" style="margin-left: 8px" data-placement="top" data-toggle="tooltip" data-original-title="Download PDF " href="'+download_url+'"><i class="fa fa-cloud-download"></i></a>';
      if (softDelete) { 
      html +='<a onclick="sales_soft_delete('+daily_data[smId].smId+',\''+daily_data[smId].receipt_no+'\')" class="btn btn-danger btn-sm  pull-right printHideA" style="margin-left: 8px" data-placement="top" data-toggle="tooltip" data-original-title="Delete"><i class="fa fa-trash-o"></i></a>';
      }
      html +='</td></tr>';
      i++;
    }
    html +='</tbody>';
    
    html +='</table>';

    return html;

  }

function sales_soft_delete(salesId, receipt_number) {
  bootbox.prompt({
      inputType:'text',
      placeholder: 'Enter Remarks',
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      title: "Deleting sales Receipt: " + receipt_number +". Are you Sure?", 
      callback: function (remarks) {
        if (remarks=='') {
          return false;
        }
        if(remarks) {        
          $.ajax({
            url: '<?php echo site_url('sales/sales/sales_receits_delete'); ?>',
            type: 'post',
            data: {'salesId' : salesId,'remarks':remarks},
            success:function(data){
              if(data){
                 location.reload();
              new PNotify({
                  title: 'Success',
                  text: 'Successfully deleted',
                  type: 'success',
                });
              } else {
               new PNotify({
                  title: 'Error',
                  text: 'Something went wrong',
                  type: 'Error',
                });
              }
            }
          });
        }
      }
  });
}


</script>

<style type="text/css">
  @page {
    size: auto;
}

.buttons-print{
  background: none;
  border: none;
}
.buttons-excel{
  background: none;
  border: none;
}
.dt-buttons{
  text-align: right;
  /*position: absolute;*/
}
</style>
<script type="text/javascript">
  function printProfile(){

    var restorepage = document.body.innerHTML;
    $('#print_visible').css('display','block');
    $('.printHide').css('display','none !important');
    $('.printHideA').css('display','none !important');
    $('.printHideA').hide();
    $('.printShow').css('display','');

    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;

  }

  function exportToExcel_daily(){

     var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };
    var mainTable = $(".salesTrans").html();

    $('.printHide').css('display','none !important');
    $('.printHideA').css('display','none !important');
    $('.printHideA').hide();
    $('.printShow').css('display','');


    htmls = mainTable;
    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }
    var link = document.createElement("a");
    link.download = "sales_tx_detailed_report.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();
  }
</script>